<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Add the base tag here -->
    <base href=".">    
    <title>JavaScript Dependency Visualizer</title>
    <!-- Handle and prevent CSS MIME type errors during scanning -->
    <script>
        // Create a style element that will handle all CSS content with proper MIME type
        const styleElement = document.createElement('style');
        styleElement.id = 'css-interceptor';
        styleElement.textContent = '/* CSS interceptor active */';
        document.head.appendChild(styleElement);
        
        // 1. Add meta tag to disable stylesheet loading completely
        const metaCSP = document.createElement('meta');
        metaCSP.httpEquiv = 'Content-Security-Policy';
        metaCSP.content = "style-src 'self' 'unsafe-inline'; style-src-elem 'self' 'unsafe-inline'";
        document.head.appendChild(metaCSP);
        
        // 2. Override fetch API to handle CSS files with proper MIME type
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            if (typeof url === 'string' && url.toString().endsWith('.css')) {
                console.log('Intercepted CSS fetch:', url);
                return Promise.resolve(new Response('/* CSS intercepted */', {
                    status: 200,
                    headers: { 'Content-Type': 'text/css' }
                }));
            }
            return originalFetch.apply(this, arguments);
        };
        
        // 3. Override XMLHttpRequest for CSS files
        const originalOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...rest) {
            if (typeof url === 'string' && url.toString().endsWith('.css')) {
                console.log('Intercepted CSS XHR:', url);
                this.abort = function() {};
                this.send = function() {
                    setTimeout(() => {
                        Object.defineProperty(this, 'status', { value: 200 });
                        Object.defineProperty(this, 'statusText', { value: 'OK' });
                        Object.defineProperty(this, 'responseText', { value: '/* CSS intercepted */' });
                        Object.defineProperty(this, 'response', { value: '/* CSS intercepted */' });
                        Object.defineProperty(this, 'readyState', { value: 4 });
                        if (this.onreadystatechange) this.onreadystatechange();
                        if (this.onload) this.onload();
                    }, 0);
                };
                return;
            }
            return originalOpen.apply(this, arguments);
        };
        
        // 4. Intercept and block all link elements with rel=stylesheet
        const originalAppendChild = Element.prototype.appendChild;
        Element.prototype.appendChild = function(child) {
            if (child.tagName === 'LINK' && 
                (child.rel === 'stylesheet' || child.getAttribute('rel') === 'stylesheet')) {
                console.log('Blocked appending stylesheet link:', child.href || child.getAttribute('href'));
                // Create a fake element instead of the real link
                const fakeElement = document.createElement('meta');
                fakeElement.dataset.originalHref = child.href || child.getAttribute('href');
                fakeElement.dataset.blocked = 'css-link';
                return originalAppendChild.call(this, fakeElement);
            }
            return originalAppendChild.call(this, child);
        };
        
        // 5. Prevent HTML parsing from adding CSS links
        const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
        Object.defineProperty(Element.prototype, 'innerHTML', {
            set: function(html) {
                // If the HTML contains link tags with stylesheets, remove them
                if (typeof html === 'string' && html.includes('stylesheet')) {
                    const tempDiv = document.createElement('div');
                    originalInnerHTML.set.call(tempDiv, html);
                    
                    // Find and remove all stylesheet links
                    const links = tempDiv.querySelectorAll('link[rel="stylesheet"]');
                    links.forEach(link => {
                        console.log('Removed stylesheet link from innerHTML:', link.href);
                        link.parentNode.removeChild(link);
                    });
                    
                    // Set the sanitized HTML
                    return originalInnerHTML.set.call(this, tempDiv.innerHTML);
                }
                return originalInnerHTML.set.call(this, html);
            },
            get: originalInnerHTML.get
        });
        
        // 6. Monitor DOM changes to catch any missed stylesheet links
        
        // 7. Patch the createElement method to prevent stylesheet creation
        const originalCreateElement = document.createElement.bind(document);
        document.createElement = function(tagName) {
            const element = originalCreateElement(tagName);
            if (tagName.toLowerCase() === 'link') {
                // Override the setAttribute method for link elements
                const originalSetAttribute = element.setAttribute.bind(element);
                element.setAttribute = function(name, value) {
                    if ((name === 'rel' && value === 'stylesheet') || 
                        (name === 'href' && element.rel === 'stylesheet')) {
                        console.log('Blocked setting attribute:', name, value);
                        return;
                    }
                    return originalSetAttribute(name, value);
                };
                
                // Disable setting href property directly
                Object.defineProperty(element, 'href', {
                    set: function(value) {
                        if (this.rel === 'stylesheet') {
                            console.log('Blocked setting href property:', value);
                            return;
                        }
                        originalSetAttribute.call(this, 'href', value);
                    }
                });
            }
            return element;
        };
        
        // 4. Use MutationObserver as a backup to catch any elements that got through
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Handle LINK elements
                            if (node.tagName === 'LINK' && 
                                (node.getAttribute('rel') === 'stylesheet' || 
                                 node.getAttribute('type') === 'text/css')) {
                                // Remove the href attribute to prevent loading
                                const originalHref = node.getAttribute('href');
                                node.removeAttribute('href');
                                //console.log(`Prevented loading of CSS file: ${originalHref}`);
                            }
                            
                            // Also check for any link elements inside the new node
                            const links = node.querySelectorAll('link[rel="stylesheet"], link[type="text/css"]');
                            links.forEach(link => {
                                const href = link.getAttribute('href');
                                link.removeAttribute('href');
                                console.log(`Prevented loading of nested CSS file: ${href}`);
                            });
                        }
                    });
                }
            });
        });
        
        // Start observing with the configured parameters
        observer.observe(document.documentElement, { childList: true, subtree: true });
    </script>
    <link href="./css/main.css" rel="stylesheet" />
    <!-- Include D3.js library for visualizations -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <!-- Main application script (ES modules) -->
    <script type="module" src="./js/main.js"></script>
</head>
<body>

    <!-- Main Application Container -->
    <div class="app-container">
        <!-- Sidebar (Left Navigation) -->
        <div class="sidebar">
            <div class="sidebar-header">
                NanoML Explorer
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-item active" id="nav-dep-visualizer">
                    <span class="sidebar-item-icon icon">
                        <svg viewBox="0 0 24 24"><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12z"></path></svg> <!-- Chat icon -->
                    </span>
                    <span>Dependency Visualizer</span>
                </div>
                <div class="sidebar-item" id="nav-file-explorer">
                    <span class="sidebar-item-icon icon">
                        <svg viewBox="0 0 24 24"><path d="M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z"></path></svg> <!-- Folder icon -->
                    </span>
                    <span>File Explorer</span>
                </div>
                <div class="sidebar-item" id="nav-settings">
                    <span class="sidebar-item-icon icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-hidden="true">
                            <defs>
                                <path id="cog-shape" d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49 1c-.23.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z"/>
                            </defs>
                            <use xlink:href="#cog-shape" fill="#FF007F" transform="translate(-0.3, 0)" opacity="0.7"/>
                            <use xlink:href="#cog-shape" fill="#00FFFF" transform="translate(0.3, 0)" opacity="0.7"/>
                            <use xlink:href="#cog-shape" fill="white" fill-rule="evenodd"/>
                        </svg> <!-- Settings/Cog icon -->
                    </span>
                    <span>Settings</span>
                </div>
            </div>
            <div class="sidebar-footer-text">
                <button class="btn-primary btn-sm">
                    <span class="icon" style="margin-right: var(--spacing-xs); display:inline-flex; align-items:center;">
                        <svg viewBox="0 0 24 24" width="16" height="16"><path d="M12.65 10C11.83 7.67 9.61 6 7 6c-3.31 0-6 2.69-6 6s2.69 6 6 6c2.61 0 4.83-1.67 5.65-4H17v4h2v-4h2v-4H12.65zM7 14c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"></path></svg> <!-- Key Icon -->
                    </span>
                    Get API key
                </button>
                <br/>
                <br/>
                This experimental model is for feedback and testing only. No production use.
            </div>
        </div>

        <!-- Wrapper for Main Content -->
        <div class="app-container">
            <!-- Right panel for details -->
            <div id="details-panel-container" class="details-panel-container">
                <div class="details-panel-header">
                    <h3>File Details</h3>
                    <button id="close-details-panel" class="close-button">&times;</button>
                </div>
                <div id="file-details-panel" class="details-panel-content"></div>
            </div>

                <div class="content-scroll-area">
                    <div class="card" id="project-selection-card" style="flex: 0 0 auto;">
                        <div class="card-header">
                            <div class="card-title">Project Selection</div>
                        </div>
                        <div class="card-content" style="display: flex; flex-wrap: wrap; align-items: center; gap: var(--spacing-md);">
                            <div style="flex: 1; min-width: 250px;">
                                <input type="file" id="project-folder-input" webkitdirectory directory multiple>
                            </div>
                            <div id="process-buttons-container" style="flex: 0 0 auto; display: flex; gap: var(--spacing-sm);">
                                <button id="process-files-btn">Process Files</button>
                                <button id="stop-process-btn" class="text-button">Stop</button>
                            </div>
                            <div style="flex-basis: 100%; margin-top: var(--spacing-sm);">
                                <div id="status">Waiting for action...</div>
                                <div id="progress-bar-container">
                                    <div id="progress-bar"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card" id="visualization-card" style="flex: 1; min-height: 500px;">
                        <div class="card-header">
                            <div class="card-title">Dependency Mind Map</div>
                            <div class="card-actions">
                                <button id="social-map" class="text-button">Social Map</button>
                                <button id="technical-map" class="text-button">Technical Map</button>
                                <button id="logic-chart" class="text-button">Logic Chart</button>
                                <button id="export-svg" class="text-button">Export SVG</button>
                                <button id="export-png" class="text-button">Export PNG</button>
                            </div>
                        </div>
                        <div id="visualization-container" class="card-content" style="padding: 0; display: flex; flex-direction: column; position: relative; align-content: flex-start; justify-content: flex-start; align-items: flex-start;">
                              <!-- Visualizer containers will be created programmatically -->
                        </div>
                    </div>

                    <div class="card" id="actions-card" style="flex: 0 0 auto;">
                        <div class="card-header">
                            <div class="card-title">Actions</div>
                            <div class="card-actions">
                                <!-- Buttons moved to Dependency Graph header -->
                            </div>
                        </div>
                        <div class="card-content" style="display: flex; gap: var(--spacing-sm); flex-wrap: wrap;">
                            <button id="export-json">Export JSON</button>
                            <button id="import-json">Import JSON</button>
                            <input type="file" id="json-file-input" accept=".json" style="display: none;">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Debug Button for Modal Testing -->
    <!-- <button id="debug-modal-button" style="position: fixed; bottom: 10px; right: 10px; z-index: 9999; padding: 5px 10px; background: red; color: white;">Test Modal</button> -->
    
    <!-- Modal for showing file content -->
    <div id="file-content-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="document.getElementById('file-content-modal').style.display='none'">&times;</span>
                <h3 id="file-content-title">File Content</h3>
            </div>
            <div class="modal-body">
                <div id="file-content" class="file-content"></div>
            </div>
        </div>
    </div>
    
    <!-- Code Preview Modal -->
    <div id="code-preview-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="document.getElementById('code-preview-modal').style.display='none'">&times;</span>
                <h3 id="code-preview-title">Code Preview</h3>
            </div>
            <div class="modal-body">
                <div id="code-preview-content" class="code-preview-content"></div>
            </div>
        </div>
    </div>

    <!-- Load the wrapper script that makes DependencyMindMap available globally -->
    <script src="dependency-mindmap.js"></script>

    <script>
(function() {
    // Cached DOM elements
    const DOM = {
        detailsPanel: document.getElementById('details-panel'),
        closeDetailsPanelBtn: document.getElementById('close-details-panel'),
        visualization: document.getElementById('mindmap-container'),
        nodeInfoContent: document.getElementById('node-info-content'),
        panelContainer: document.getElementById('details-panel-container'),
        fileDetailsPanel: document.getElementById('file-details-panel'),
        detailsSearch: document.getElementById('details-search'),
        themeToggleButton: document.getElementById('theme-toggle'),
        themeIconSvg: document.getElementById('theme-icon-svg'),
        projectFolderInput: document.getElementById('project-folder-input'),
        processFilesBtn: document.getElementById('process-files-btn'),
        stopProcessBtn: document.getElementById('stop-process-btn'),
        status: document.getElementById('status'),
        progressBarContainer: document.getElementById('progress-bar-container'),
        progressBar: document.getElementById('progress-bar'),
        codePreviewModal: document.getElementById('code-preview-modal'),
        codePreviewTitle: document.getElementById('code-preview-title'),
        codePreviewContent: document.getElementById('code-preview-content'),
        jsonFileInput: document.getElementById('json-file-input'),
        exportJson: document.getElementById('export-json'),
        importJson: document.getElementById('import-json'),
        socialMap: document.getElementById('social-map'),
        technicalMap: document.getElementById('technical-map'),
        logicChart: document.getElementById('logic-chart'),
        exportSvg: document.getElementById('export-svg'),
        exportPng: document.getElementById('export-png')
    };

    // Constants
    const WORKER_THRESHOLD = 500;
    const WORKER_TIMEOUT = 30000;
    const SVG_NS = 'http://www.w3.org/2000/svg';
    const THEME_KEY = 'theme';
    const DEFAULT_EXCLUDE_RULES = {
        files: ['*.min.js', '*.config.js', '*.md', '*.css'],
        folders: ['node_modules/', '.git/', 'dist/']
    };
    const SUN_ICON = '<path d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1zm18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1zm-9-7c.55 0 1-.45 1-1V3c0-.55-.45-1-1-1s-1 .45-1 1v2c0 .55.45 1 1 1zm0 12c.55 0 1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1v2c0 .55.45 1 1 1zM5.64 7.05l-1.41-1.41c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41l1.41 1.41c.39.39 1.02.39 1.41 0 .4-.39.4-1.02.01-1.41zm12.72 12.72l-1.41-1.41c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41l1.41 1.41c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41zM19.78 5.64l-1.41 1.41c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0l1.41-1.41c.39-.39.39-1.02 0-1.41-.39-.39-1.02-.39-1.41 0zm-12.72 12.72l-1.41 1.41c-.39-.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0l1.41-1.41c.39-.39.39-1.02 0-1.41-.39-.39-1.02-.39-1.41 0z"></path>';
    const MOON_ICON = '<path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9c0-.46-.04-.92-.1-1.36-.98 1.37-2.58 2.26-4.4 2.26-2.98 0-5.4-2.42-5.4-5.4 0-1.82.89-3.42 2.26-4.4-.44-.06-.9-.1-1.36-.1z"></path>';

    // State
    let activeWorkers = [];
    let totalFilesProcessed = 0;
    let totalFilesExpected = 0;
    let workerResults = [];
    let workerLastActive = {};
    let isLightTheme = localStorage.getItem(THEME_KEY) === 'light';
    let excludedFolders = DEFAULT_EXCLUDE_RULES.folders;

    // Utility Functions
    const escapeHtml = text => text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');

    // Function to show code preview in a modal
    window.showCodePreview = function(filePath, lineNumber, itemName) { // lineNumber is original 1-based
        console.log('Showing code preview for:', filePath, 'line:', lineNumber, 'item:', itemName);
        
        const modal = DOM.codePreviewModal; // Use cached DOM element
        const modalTitle = DOM.codePreviewTitle;
        const modalContent = DOM.codePreviewContent;
        
        if (!modal || !modalTitle || !modalContent) {
            console.error('Code preview modal elements not found');
            alert('Code preview modal elements not found. Please check the console for details.');
            return;
        }
        
        modalTitle.textContent = `Preview: ${itemName} (line ${lineNumber} in ${filePath.split('/').pop()})`;  
        
        let codeSnippetForDisplay = null;
        let snippetOriginalStartLine = 1; 

        // Priority 1: Full file content from scanResults.fileContents (if enabled and available)
        // This part is hypothetical as fileContents is not explicitly populated by the current worker for preview.
        // If you have a mechanism to store full file contents, it would go here.
        // For now, we'll rely on codeMap or re-slicing larger codeMap entries.

        // Priority 2: Specific codeMap entry created by the worker (should have 5 lines before/after)
        if (window.scanResults && window.scanResults.codeMap && window.scanResults.codeMap[filePath]) {
            const codeMapEntries = window.scanResults.codeMap[filePath];
            // Find the relevant entry. Match by original line number and item name/type.
            const entry = codeMapEntries.find(item => {
                return item.line === lineNumber &&
                       ((item.function && item.function === itemName) ||
                        (item.variable && item.variable === itemName) ||
                        (item.event && item.event === itemName)
                       );
            });

            if (entry && entry.preview && typeof entry.previewStartLine === 'number') {
                codeSnippetForDisplay = entry.preview;
                snippetOriginalStartLine = entry.previewStartLine;
                console.log(`Using specific codeMap entry for ${itemName} at line ${lineNumber}. Snippet starts at ${snippetOriginalStartLine}.`);
            }
        }

        // Fallback: If no specific 5-before/after snippet, try to use file header or general import context
        // This is less ideal for the "5 lines before/after" requirement but provides some context.
        if (!codeSnippetForDisplay && window.scanResults && window.scanResults.codeMap && window.scanResults.codeMap[filePath]) {
            const codeMapEntries = window.scanResults.codeMap[filePath];
            // Attempt to find any snippet that contains the line
            let bestFallbackEntry = null;
            for (const item of codeMapEntries) {
                if (item.preview && typeof item.previewStartLine === 'number') {
                    const snippetLinesCount = item.preview.split('\n').length;
                    if (lineNumber >= item.previewStartLine && lineNumber < (item.previewStartLine + snippetLinesCount)) {
                        // This snippet contains the line. Prefer function/variable/event snippets.
                        if (item.function || item.variable || item.event) {
                            bestFallbackEntry = item;
                            break; 
                        }
                        if (!bestFallbackEntry) bestFallbackEntry = item; // Take any if no specific type found yet
                    }
                }
            }
            
            if (bestFallbackEntry) {
                console.log(`Using fallback codeMap entry for ${itemName} at line ${lineNumber}. Original snippet starts at ${bestFallbackEntry.previewStartLine}.`);
                // We have a snippet that contains the line, but it might not be centered with 5 lines before/after.
                // We need to re-slice this snippet to get the desired 5-before/after context around lineNumber.
                const allSnippetLines = bestFallbackEntry.preview.split('\n');
                const relativeTargetLineInSnippet = lineNumber - bestFallbackEntry.previewStartLine; // 0-based

                const startSliceIndexInSnippet = Math.max(0, relativeTargetLineInSnippet - 5);
                const endSliceIndexInSnippet = Math.min(allSnippetLines.length, relativeTargetLineInSnippet + 5 + 1);
                
                codeSnippetForDisplay = allSnippetLines.slice(startSliceIndexInSnippet, endSliceIndexInSnippet).join('\n');
                snippetOriginalStartLine = bestFallbackEntry.previewStartLine + startSliceIndexInSnippet;
            }
        }
        
        if (!codeSnippetForDisplay) {
            modalContent.innerHTML = '<pre class="code-preview-pre">Code snippet not available for the selected item.</pre>';
            modal.style.display = 'block';
            return;
        }

        displayCodeContent(codeSnippetForDisplay, snippetOriginalStartLine, lineNumber, modalContent);
        modal.style.display = 'block';
        
        // Close modal on click outside
        // Ensure this is only added once or managed properly if showCodePreview is called multiple times
        const closeModalHandler = function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
                window.removeEventListener('click', closeModalHandler); // Clean up listener
            }
        };
        window.addEventListener('click', closeModalHandler);
    };
    
    // Helper function to display code content with line highlighting
    // codeSnippet: The string of code (e.g., the 5 before/after window)
    // snippetOriginalStartLine: The original line number in the full file where this snippet begins (1-based)
    // targetOriginalLineNumber: The original line number in the full file that should be highlighted (1-based)
    // modalContentEl: The DOM element to put the HTML into
    function displayCodeContent(codeSnippet, snippetOriginalStartLine, targetOriginalLineNumber, modalContentEl) {
        const lines = String(codeSnippet).split('\n'); // Ensure codeSnippet is a string
        let html = '<pre class="code-preview-pre"><code>';

        // Calculate the 0-based index of the target line within the current snippet
        const highlightIndexInSnippet = targetOriginalLineNumber - snippetOriginalStartLine;

        for (let i = 0; i < lines.length; i++) {
            const currentOriginalLineNum = snippetOriginalStartLine + i; // Line number to display (1-based)
            const lineClass = i === highlightIndexInSnippet ? 'highlighted-line' : '';
            
            html += `<div class="${lineClass}" data-line="${currentOriginalLineNum}">`;
            html += `<span class="line-number">${currentOriginalLineNum}</span>${escapeHtml(lines[i] || '')}</div>`;
        }
        
        html += '</code></pre>';
        
        // Add copy button for the highlighted line
        if (highlightIndexInSnippet >= 0 && highlightIndexInSnippet < lines.length) {
            const highlightedCode = lines[highlightIndexInSnippet] || '';
            html += `<div class="code-actions">`;
            html += `<button class="copy-btn" data-clipboard-text="${escapeHtml(highlightedCode)}">Copy Line ${targetOriginalLineNumber}</button>`;
            html += `</div>`;
        }
        
        modalContentEl.innerHTML = html;

        // Add event listener to copy button
        const copyButton = modalContentEl.querySelector('.copy-btn');
        if (copyButton) {
            copyButton.addEventListener('click', function() {
                navigator.clipboard.writeText(this.dataset.clipboardText)
                    .then(() => {
                        // Optional: Show feedback
                        const originalText = this.textContent;
                        this.textContent = 'Copied!';
                        setTimeout(() => { this.textContent = originalText; }, 1500);
                    })
                    .catch(err => console.error('Failed to copy text: ', err));
            });
        }
    }

    // Global function to handle direct clicks on code items
    window.handleCodePreviewClick = function(e) {
        console.log('handleCodePreviewClick called', e);
        const target = e.currentTarget || e.target;
        console.log('Target element:', target);
        
        // First try to get data from data attributes (newer format)
        let itemName = target.getAttribute('data-name');
        let lineNumber = target.getAttribute('data-line');
        let filePath = target.getAttribute('data-file-path');
        console.log('Data attributes - name:', itemName, 'line:', lineNumber, 'file:', filePath);
        
        // Special handling for event items
        if (itemName && (itemName.toLowerCase() === 'on' || itemName.startsWith('on')) && target.closest('.events-list')) {
            // First try to get the file path from the data attribute on the element
            const eventData = target.getAttribute('data-event');
            if (eventData) {
                try {
                    const parsedEvent = JSON.parse(eventData);
                    if (parsedEvent && parsedEvent.filePath) {
                        filePath = parsedEvent.filePath;
                        lineNumber = parsedEvent.line || lineNumber;
                        console.log('Using file path from event data attribute:', filePath, 'line:', lineNumber);
                    }
                } catch (e) {
                    console.error('Failed to parse event data:', e);
                }
            }
            
            // If still no file path, look for it in the scan results
            if (!filePath && window.scanResults && window.scanResults.files) {
                // Try to find the event in all files
                for (const [path, fileData] of Object.entries(window.scanResults.files)) {
                    if (fileData.events && Array.isArray(fileData.events)) {
                        // Look for matching event
                        const matchingEvent = fileData.events.find(event => 
                            (event.name === itemName || event.name === itemName.toLowerCase()) && 
                            (!lineNumber || event.line === lineNumber)
                        );
                        
                        if (matchingEvent) {
                            filePath = matchingEvent.filePath || path;
                            lineNumber = matchingEvent.line || lineNumber;
                            console.log('Found matching event in scan results:', filePath, 'line:', lineNumber);
                            break;
                        }
                    }
                }
            }
            
            // If still no file path, try to extract from context
            if (!filePath) {
                // For events, we need to find the correct file path from the current context
                // Look for the nearest parent with file information
                const detailsPanel = target.closest('#details-panel-container');
                if (detailsPanel) {
                    const fileInfoElement = detailsPanel.querySelector('.file-info');
                    if (fileInfoElement) {
                        // Extract the correct file path from the file info section
                        const fileInfoText = fileInfoElement.textContent;
                        if (fileInfoText.includes('File:')) {
                            filePath = fileInfoText.split('File:')[1].split('\n')[0].trim();
                            console.log('Extracted file path from file info for event:', filePath);
                        }
                    }
                    
                    // Also check for any data attribute on the container
                    if (!filePath) {
                        filePath = detailsPanel.getAttribute('data-current-file') || detailsPanel.getAttribute('data-file-path');
                        console.log('File path from details panel data attribute:', filePath);
                    }
                }
                
                // Check if we're in a dependency view with a selected node
                if (!filePath && window.selectedNode && window.selectedNode.data) {
                    filePath = window.selectedNode.data.filePath || window.selectedNode.data.file;
                    console.log('File path from selected node in dependency view:', filePath);
                }
            }
        }
        
        // If not found, try to parse from text content (older format)
        if (!itemName || !lineNumber) {
            const text = target.textContent;
            console.log('Trying to parse from text:', text);
            
            // Try pattern with parentheses: "name (line 123)"
            let matches = text.match(/(.+?)\s*\(line\s*(\d+)\)/i);
            
            // Try pattern with "Line:" format: "name Line: 123"
            if (!matches) {
                matches = text.match(/(.+?)\s*Line:\s*(\d+)/i);
            }
            
            if (matches && matches.length > 2) {
                itemName = matches[1].trim();
                lineNumber = matches[2];
                console.log('Parsed from text - name:', itemName, 'line:', lineNumber);
            }
        }
        
        // Convert lineNumber to integer
        lineNumber = parseInt(lineNumber);
        
        if (!itemName || !lineNumber) {
            console.error('Could not extract item name or line number from:', target);
            return;
        }
        
        // If file path not found from special handling, try standard methods
        if (!filePath) {
            // Try parent element
            if (target.parentElement) {
                filePath = target.parentElement.getAttribute('data-file-path');
                console.log('File path from parent element data attribute:', filePath);
            }
            
            // If still not found, get from the stored global variable
            if (!filePath) {
                filePath = window.currentFilePath;
                console.log('Current file path from global variable:', filePath);
            }
            
            // Try panel container data attribute
            if (!filePath) {
                const panelContainer = document.getElementById('details-panel-container');
                if (panelContainer) {
                    filePath = panelContainer.getAttribute('data-file-path') || panelContainer.getAttribute('data-current-file');
                    console.log('File path from panel data attribute:', filePath);
                }
            }
            
            // Try lastNodeSelected
            if (!filePath && window.scanResults?.lastNodeSelected) {
                filePath = window.scanResults.lastNodeSelected._filePath || window.scanResults.lastNodeSelected.filePath;
                console.log('File path from lastNodeSelected:', filePath);
            }
            
            // Try to get file path from the panel content as a last resort
            if (!filePath) {
                // Look for any element with file path information
                const filePathElements = document.querySelectorAll('.details-section li');
                for (const el of filePathElements) {
                    const text = el.textContent;
                    if (text.includes('File Path:') || text.includes('File:')) {
                        const splitText = text.includes('File Path:') ? text.split('File Path:') : text.split('File:');
                        if (splitText.length > 1) {
                            filePath = splitText[1].split('\n')[0].trim();
                            console.log('Extracted file path from panel content:', filePath);
                            break;
                        }
                    }
                }
            }
        }
        
        console.log('Final file path for code preview:', filePath);
        console.log('Item name:', itemName, 'Line number:', lineNumber);
        
        if (!filePath) {
            console.error('No file path found for code preview');
            return;
        }
        
        // Use the showCodePreview function to display the code
        showCodePreview(filePath, lineNumber, itemName);
        
        // Prevent event from bubbling up
        e.stopPropagation();
    };
    
    // Define handleCodePreview function for event delegation
    const handleCodePreview = e => {
        // This is a simpler version that delegates to the global handleCodePreviewClick function
        if (e.target.tagName === 'LI' && e.target.closest('#file-details-panel')) {
            window.handleCodePreviewClick(e);
        }
    };
    
    const createSectionHTML = (title, items, className, formatter) => {
    if (!items?.length || !Array.isArray(items) || typeof formatter !== 'function') return '';
    
    // Special handling for events section to include file path data
    const isEventsSection = className === 'events-title' || title.includes('Events');
    
    return `
        <div class="details-section ${isEventsSection ? 'events-list' : ''}">
            <h3 class="section-title ${className}">${title} (${items.length})</h3>
            <ul>${items.map(item => {
                // Prepare data attributes based on item type
                let dataAttrs = `data-line="${item.line || ''}" data-name="${typeof item === 'string' ? item : (item.name || 'unknown')}"`;
                
                // For events, add the file path and full event data
                if (isEventsSection && typeof item === 'object') {
                    // Store file path explicitly
                    if (item.filePath) {
                        dataAttrs += ` data-file-path="${item.filePath}"`;
                    }
                    
                    // Store the entire event object as JSON for complete data
                    const eventData = JSON.stringify(item).replace(/"/g, '&quot;');
                    dataAttrs += ` data-event="${eventData}"`;
                }
                
                return `<li class="code-item${isEventsSection ? ' event-item' : ''}" ${dataAttrs} onclick="handleCodePreviewClick(event)">${formatter(item)}</li>`;
            }).join('')}</ul>
        </div>
    `;
};

const loadExcludeRules = () => fetch('exclude.json')
        .then(response => response.ok ? response.json() : Promise.reject(`Failed to load exclude.json: ${response.status}`))
        .then(data => (excludedFolders = data.folders || excludedFolders, data))
        .catch(() => DEFAULT_EXCLUDE_RULES);

    // Search filter function for the details panel
    const handleSearchFilter = e => {
        if (e.target.id !== 'details-search') return;
        const searchText = e.target.value.toLowerCase();
        const detailItems = DOM.fileDetailsPanel?.querySelectorAll('li, h3') || [];
        detailItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (item.tagName === 'H3') {
                const section = [];
                let next = item.nextElementSibling;
                while (next && next.tagName !== 'H3') {
                    section.push(next);
                    next = next.nextElementSibling;
                }
                const hasMatch = section.some(el => el.textContent.toLowerCase().includes(searchText));
                item.style.display = hasMatch || text.includes(searchText) ? '' : 'none';
                section.forEach(el => {
                    el.style.display = (hasMatch || el.textContent.toLowerCase().includes(searchText)) ? '' : 'none';
                });
            } else if (!item.previousElementSibling || item.previousElementSibling.tagName !== 'H3' || item.previousElementSibling.style.display !== 'none') {
                item.style.display = text.includes(searchText) ? '' : 'none';
            }
        });
    };

    // Panel Management
    const populateRightPanelWithScanResults = (scanResults) => {
        if (!scanResults || !DOM.fileDetailsPanel) return;
        
        const totalFiles = Object.keys(scanResults.files || {}).length;
        const totalNodes = scanResults.graph?.nodes?.length || 0;
        const totalEdges = scanResults.graph?.edges?.length || 0;
        
        // Count different types of items
        let totalFunctions = 0;
        let totalVariables = 0;
        let totalImports = 0;
        let totalExports = 0;
        let totalEvents = 0;
        
        Object.values(scanResults.files || {}).forEach(file => {
            totalFunctions += (file.functions || []).length;
            totalVariables += (file.variables || []).length;
            totalImports += (file.imports || []).length;
            totalExports += (file.exports || []).length;
            totalEvents += (file.events || []).length;
        });
        
        const scanSummaryHTML = `
            <div class="details-section">
                <h3 class="section-title scan-summary-title">📊 Scan Results Summary</h3>
                <ul>
                    <li><strong>Total Files Scanned:</strong> ${totalFiles}</li>
                    <li><strong>Graph Nodes:</strong> ${totalNodes}</li>
                    <li><strong>Graph Connections:</strong> ${totalEdges}</li>
                    <li><strong>Functions/Classes:</strong> ${totalFunctions}</li>
                    <li><strong>Variables:</strong> ${totalVariables}</li>
                    <li><strong>Imports:</strong> ${totalImports}</li>
                    <li><strong>Exports:</strong> ${totalExports}</li>
                    <li><strong>Events:</strong> ${totalEvents}</li>
                    <li><strong>Last Updated:</strong> ${new Date(scanResults.lastUpdated).toLocaleString()}</li>
                </ul>
            </div>
        `;
        
        // Get top files by complexity (most functions + variables)
        const fileComplexity = Object.entries(scanResults.files || {}).map(([path, file]) => ({
            path,
            complexity: (file.functions || []).length + (file.variables || []).length,
            functions: (file.functions || []).length,
            variables: (file.variables || []).length,
            imports: (file.imports || []).length,
            exports: (file.exports || []).length
        })).sort((a, b) => b.complexity - a.complexity).slice(0, 10);
        
        const topFilesHTML = fileComplexity.length > 0 ? `
            <div class="details-section">
                <h3 class="section-title top-files-title">🏆 Most Complex Files</h3>
                <ul>
                    ${fileComplexity.map(file => `
                        <li class="file-item" data-file-path="${file.path}" style="margin-bottom: 12px; padding: 8px; border: 1px solid rgba(255,255,255,0.2); border-radius: 6px;">
                            <div class="file-header" style="font-weight: bold; margin-bottom: 6px; cursor: pointer;" data-file-path="${file.path}">
                                📄 ${file.path.split('/').pop()}
                            </div>
                            <div class="file-properties" style="display: grid; grid-template-columns: 1fr 1fr; gap: 4px; font-size: 0.9em;">
                                ${file.functions > 0 ? `<div class="property-item" data-file-path="${file.path}" data-property="functions" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(100,149,237,0.2);">⚙️ Functions: ${file.functions}</div>` : ''}
                                ${file.variables > 0 ? `<div class="property-item" data-file-path="${file.path}" data-property="variables" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(255,165,0,0.2);">📊 Variables: ${file.variables}</div>` : ''}
                                ${file.imports > 0 ? `<div class="property-item" data-file-path="${file.path}" data-property="imports" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(50,205,50,0.2);">📥 Imports: ${file.imports}</div>` : ''}
                                ${file.exports > 0 ? `<div class="property-item" data-file-path="${file.path}" data-property="exports" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(255,69,0,0.2);">📤 Exports: ${file.exports}</div>` : ''}
                            </div>
                        </li>
                    `).join('')}
                </ul>
            </div>
        ` : '';
        
        // Get files with most dependencies
        const dependencyCount = Object.entries(scanResults.files || {}).map(([path, file]) => ({
            path,
            dependencies: (file.imports || []).length
        })).filter(f => f.dependencies > 0).sort((a, b) => b.dependencies - a.dependencies).slice(0, 5);
        
        const dependenciesHTML = dependencyCount.length > 0 ? `
            <div class="details-section">
                <h3 class="section-title dependencies-title">🔗 Most Dependencies</h3>
                <ul>
                    ${dependencyCount.map(file => `
                        <li class="file-dependency-item" data-file-path="${file.path}" style="cursor: pointer; padding: 6px; border-radius: 4px; margin-bottom: 4px; border: 1px solid rgba(255,255,255,0.1);">
                            <strong>${file.path.split('/').pop()}</strong> (${file.dependencies} imports)
                        </li>
                    `).join('')}
                </ul>
            </div>
        ` : '';
        
        const searchHTML = `
            <div class="search-container" style="padding: 8px; margin-bottom: 8px;">
                <input type="text" id="details-search" placeholder="Search scan results..." style="width: 100%; padding: 6px;" />
            </div>
        `;
        
        const instructionsHTML = `
            <div class="details-section">
                <h3 class="section-title instructions-title">ℹ️ Instructions</h3>
                <p>Scan complete! Click on any node in the visualization to view detailed file information, or explore the summary above. You can also click on files in the lists above to view their details.</p>
            </div>
        `;
        
        DOM.fileDetailsPanel.innerHTML = searchHTML + scanSummaryHTML + topFilesHTML + dependenciesHTML + instructionsHTML;
        
        // Add click handlers for file items
        DOM.fileDetailsPanel.querySelectorAll('.file-item, .file-dependency-item').forEach(item => {
            item.addEventListener('click', () => {
                const filePath = item.dataset.filePath;
                if (filePath && scanResults.files[filePath]) {
                    // Create a node data object from the file data
                    const nodeData = {
                        id: filePath,
                        name: filePath.split('/').pop(),
                        path: filePath,
                        type: 'file',
                        ...scanResults.files[filePath]
                    };
                    openDetailsPanel(nodeData);
                }
            });
            
            // Add hover effect
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            });
            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = '';
            });
        });
        
        // Add click handlers for property items
        DOM.fileDetailsPanel.querySelectorAll('.property-item').forEach(item => {
            item.addEventListener('click', () => {
                const filePath = item.dataset.filePath;
                const property = item.dataset.property;
                if (filePath && scanResults.files[filePath]) {
                    // Create a node data object from the file data
                    const nodeData = {
                        id: filePath,
                        name: filePath.split('/').pop(),
                        path: filePath,
                        type: 'file',
                        ...scanResults.files[filePath]
                    };
                    openDetailsPanel(nodeData, property);
                }
            });
            
            // Add hover effect for property items
            item.addEventListener('mouseenter', () => {
                item.style.transform = 'scale(1.02)';
                item.style.boxShadow = '0 2px 4px rgba(255, 255, 255, 0.1)';
            });
            item.addEventListener('mouseleave', () => {
                item.style.transform = '';
                item.style.boxShadow = '';
            });
        });
        
        // Add search functionality
        const searchInput = DOM.fileDetailsPanel.querySelector('#details-search');
        if (searchInput) {
            searchInput.addEventListener('input', handleSearchFilter);
        }
        
        // Make sections collapsible
        DOM.fileDetailsPanel.querySelectorAll('.section-title').forEach(header => {
            header.addEventListener('click', () => {
                header.classList.toggle('collapsed');
                const ul = header.nextElementSibling;
                if (ul?.tagName === 'UL') ul.classList.toggle('collapsed');
            });
        });
        
        // Open the panel
        if (DOM.panelContainer) {
            DOM.panelContainer.classList.add('open');
        }
        const contentArea = document.querySelector('.content-scroll-area');
        if (contentArea) contentArea.classList.add('panel-open');
    };

    const openDetailsPanel = (nodeData, property) => {
        if (!nodeData?.id) {
            if (DOM.fileDetailsPanel) {
                DOM.fileDetailsPanel.innerHTML = "<div class='details-section'><h3 class='section-title'>Ready for Selection</h3><p>Select a node in the graph to view its details.</p></div>";
            }
            return;
        }

        let sections = [
            `<div class="details-section">
                <h3 class="section-title file-info-title">File Information</h3>
                <ul>
                    <li><strong>ID:</strong> ${nodeData.id}</li>
                    <li><strong>Name:</strong> ${nodeData.name || 'N/A'}</li>
                    <li><strong>Type:</strong> ${nodeData.type || 'N/A'}</li>
                    <li><strong>Path:</strong> ${nodeData.path || 'N/A'}</li>
                </ul>
            </div>`
        ];

        if (nodeData.noDetailedData) {
            sections.push('<div class="details-section no-data-warning"><h3 class="section-title">No Detailed Data Available</h3><p>No detailed scan data was found for this file.</p></div>');
        }

        const formatDep = dep => {
            if (typeof dep === 'string') return dep;
            const hasValidLine = dep.line && !isNaN(parseInt(dep.line)) && parseInt(dep.line) > 1;
            const lineInfo = hasValidLine ? `(line ${dep.line})` : '';
            return (dep.isWorker || dep.type === 'worker') 
                ? `<span class="worker-connection">${dep.name || 'Unknown'} ${lineInfo}</span>`
                : `${dep.name || 'Unknown'} ${lineInfo}`;
        };

        sections.push(createSectionHTML('Dependencies', nodeData.dependencies, 'dependencies-title', formatDep));
        sections.push(createSectionHTML('Dependents', nodeData.dependents, 'dependents-title', formatDep));

        if (nodeData.imports?.length) {
            const workerImports = nodeData.imports.filter(imp => imp.type === 'worker' || imp.isWorker);
            if (workerImports.length) {
                sections.push(createSectionHTML('Worker Connections', workerImports, 'workers-title', 
                    imp => `<span class="worker-connection">${imp.name || imp.path || 'Unknown'} ${imp.line && !isNaN(parseInt(imp.line)) ? `(line ${imp.line})` : ''}</span>`));
            }
            const regularImports = nodeData.imports.filter(imp => imp.type !== 'worker' && !imp.isWorker);
            if (regularImports.length) {
                sections.push(createSectionHTML('Imports', regularImports, 'imports-title', 
                    imp => `${imp.name || imp.path || 'Unknown'} ${imp.line && !isNaN(parseInt(imp.line)) ? `(line ${imp.line})` : ''}`));
            }
        }

        if ((nodeData.type === 'html' || nodeData.type === 'Module') && 
            (nodeData.name === 'Entry point' || nodeData.path?.endsWith('index.html')) && 
            (!nodeData.imports || !nodeData.imports.some(imp => imp.type === 'worker' || imp.isWorker))) {
            sections.push('<div class="details-section"><h3 class="section-title workers-title">Worker Connections</h3><ul><li><span class="worker-connection">scanner.worker.js</span> <span class="worker-note">(Web Worker)</span></li></ul></div>');
        }

        sections.push(createSectionHTML('Exports', nodeData.exports, 'exports-title', 
            exp => `${exp.name || 'default'} (${exp.exportType || 'N/A'}) ${exp.line && !isNaN(parseInt(exp.line)) ? `(line ${exp.line})` : ''}`));
        sections.push(createSectionHTML('Functions/Classes', nodeData.functions, 'functions-title', 
            fn => `${fn.name || 'anonymous'} ${fn.type === 'class' ? '(class)' : `(${fn.params?.join(', ') || ''})`} (line ${fn.line || 'N/A'})`));
        sections.push(createSectionHTML('Variables', nodeData.variables, 'variables-title', 
            v => `${v.name || 'unnamed'} (${v.type || 'var'}) (line ${v.line || 'N/A'})`));
        sections.push(createSectionHTML('Events Handled/Dispatched', nodeData.events, 'events-title', 
            e => `${e.name || 'unnamed'} (${e.type || 'event'}) (line ${e.line || 'N/A'}) ${e.handler ? `→ ${e.handler}` : ''}`));

        if (property) {
            const section = sections.find(s => s.includes(property));
            if (section) {
                sections = [section];
            }
        }

        if (DOM.fileDetailsPanel) {
            // Add search input at the top of the panel
            const searchHTML = `
                <div class="search-container" style="padding: 8px; margin-bottom: 8px;">
                    <input type="text" id="details-search" placeholder="Filter items..." style="width: 100%; padding: 6px;" />
                </div>
            `;
            DOM.fileDetailsPanel.innerHTML = searchHTML + sections.filter(Boolean).join('');
            
            // Add event listener to the search input
            const searchInput = DOM.fileDetailsPanel.querySelector('#details-search');
            if (searchInput) {
                searchInput.addEventListener('input', handleSearchFilter);
            }
            
            // Add event listener for code preview
            DOM.fileDetailsPanel.addEventListener('click', handleCodePreview);
            
            DOM.fileDetailsPanel.querySelectorAll('.section-title').forEach(header => {
                if (!header.classList.contains('file-info-title')) {
                    /* Modal styles */
                    const modalStyles = `
                        <style>
                                width: 100%;
                                height: 100%;
                                overflow: auto;
                                background-color: rgba(0,0,0,0.8);
                            }
                            .modal-content {
                                background-color: #2a2a2a;
                                margin: 5% auto;
                                padding: 20px;
                                border: 1px solid #444;
                                width: 80%;
                                max-width: 1000px;
                                border-radius: 5px;
                                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                            }
                            .modal-header {
                                padding: 10px 0;
                                border-bottom: 1px solid #444;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                            }
                            .modal-header h3 {
                                margin: 0;
                                color: #fff;
                            }
                            .close {
                                color: #aaa;
                                float: right;
                                font-size: 28px;
                                font-weight: bold;
                                cursor: pointer;
                            }
                            .close:hover,
                            .close:focus {
                                color: #fff;
                                text-decoration: none;
                            }
                            .modal-body {
                                padding: 10px 0;
                            }
                            
                            /* Code preview styles */
                            .code-preview-content {
                                max-height: 70vh;
                                overflow-y: auto;
                                background-color: #1e1e1e;
                                border-radius: 4px;
                                padding: 10px;
                            }
                            .code-preview-pre {
                                margin: 0;
                                font-family: 'Consolas', 'Monaco', monospace;
                                font-size: 14px;
                                line-height: 1.5;
                                color: #d4d4d4;
                                white-space: pre-wrap;
                            }
                            .code-preview-pre .line-number {
                                display: inline-block;
                                width: 40px;
                                color: #858585;
                                text-align: right;
                                padding-right: 10px;
                                user-select: none;
                            }
                            .code-preview-pre .highlighted-line {
                                background-color: rgba(255, 255, 0, 0.1);
                                display: block;
                                width: 100%;
                            }
                        </style>
                    `;
                    document.head.insertAdjacentHTML('beforeend', modalStyles);
                    // If a specific property was clicked, expand that section
                    if (property) {
                        const sectionMap = {
                            'functions': 'functions-title',
                            'variables': 'variables-title', 
                            'imports': 'imports-title',
                            'exports': 'exports-title'
                        };
                        if (header.classList.contains(sectionMap[property])) {
                            // Don't collapse this section
                        } else {
                            header.classList.add('collapsed');
                            const ul = header.nextElementSibling;
                            if (ul?.tagName === 'UL') ul.classList.add('collapsed');
                        }
                    } else {
                        header.classList.add('collapsed');
                        const ul = header.nextElementSibling;
                        if (ul?.tagName === 'UL') ul.classList.add('collapsed');
                    }
                }
                header.addEventListener('click', () => {
                    header.classList.toggle('collapsed');
                    const ul = header.nextElementSibling;
                    if (ul?.tagName === 'UL') ul.classList.toggle('collapsed');
                });
            });
        }

        if (DOM.panelContainer) {
            DOM.panelContainer.classList.add('open');
        }
        const contentArea = document.querySelector('.content-scroll-area');
        if (contentArea) contentArea.classList.add('panel-open');
    };

    // Make openDetailsPanel globally available for other components
    window.openDetailsPanel = openDetailsPanel;

    const closeDetailsPanel = () => {
        if (DOM.fileDetailsPanel) {
            DOM.fileDetailsPanel.innerHTML = "<div class='details-section'><h3 class='section-title'>Ready for Selection</h3><p>Select a node in the graph to view its details.</p></div>";
        }
        if (DOM.nodeInfoContent) {
            DOM.nodeInfoContent.innerHTML = "<h3>File Details (Node Info)</h3><p>Click on a node in the visualization to see details.</p>";
        }
        const contentArea = document.querySelector('.content-scroll-area');
        if (contentArea) contentArea.classList.add('panel-open');
        const overlay = document.getElementById('panel-event-blocker');
        if (overlay) overlay.remove();
        window.justOpenedPanel = false;
    };

    // Theme Management
    const applyTheme = () => {
        document.body.classList.toggle('light-theme-active', isLightTheme);
        if (DOM.themeIconSvg) {
            DOM.themeIconSvg.innerHTML = isLightTheme ? MOON_ICON : SUN_ICON;
        }
    };

    applyTheme();

    // Worker Management
    const cleanupWorkers = () => {
        activeWorkers.forEach(worker => worker.terminate());
        activeWorkers = [];
    };

    const startWorker = () => {
        try {
            const worker = new Worker('js/core/scanner.worker.js'); // Correct path relative to index.html
            activeWorkers.push(worker);
            return worker;
        } catch (e) {
            DOM.status.textContent = `Error: Could not create scanner worker. ${e.message}`;
            return null;
        }
    };

    const distributeFilesToWorkers = async (filesToProcess, skipOldChart = false) => {
        workerResults = [];
        const numWorkers = filesToProcess.length > WORKER_THRESHOLD ? Math.min(4, Math.ceil(filesToProcess.length / 500)) : 1;
        const filesPerWorker = Math.ceil(filesToProcess.length / numWorkers);
        const excludeRules = await loadExcludeRules();

        for (let i = 0; i < numWorkers; i++) {
            const chunk = filesToProcess.slice(i * filesPerWorker, Math.min((i + 1) * filesPerWorker, filesToProcess.length));
            if (!chunk.length) continue;

            const worker = startWorker();
            if (!worker) continue;

            const workerId = activeWorkers.length - 1;
            const totalWorkers = activeWorkers.length - 1;
            workerLastActive[workerId] = Date.now();

            worker.postMessage({ type: 'init', totalFilesToExpect: chunk.length, workerId, totalWorkers, excludeRules });
            worker.postMessage({ type: 'fileChunk', files: chunk });
            worker.postMessage({ type: 'processAll' });

            worker.onmessage = ({ data }) => {
                if (data.workerId !== undefined) workerLastActive[data.workerId] = Date.now();

                if (data.type === 'workerResults') {
                    workerResults.push(data.data);
                    DOM.status.textContent = `Worker ${data.workerId} finished. Received ${workerResults.length}/${activeWorkers.length} results.`;

                    if (workerResults.length === activeWorkers.length) {
                        DOM.status.textContent = 'All workers finished. Combining results...';
                        const combinedFiles = {};
                        const combinedNodes = [];
                        const combinedEdges = [];
                        const combinedFileContents = {};
                        const combinedCodeMap = {};

                        workerResults.forEach(result => {
                            if (!result) return;
                            Object.assign(combinedFiles, Object.fromEntries(
                                Object.entries(result.files || {}).map(([filePath, fileData]) => [filePath, {
                                    functions: fileData.functions || [],
                                    variables: fileData.variables || [],
                                    events: fileData.events || [],
                                    imports: fileData.imports || [],
                                    exports: fileData.exports || []
                                }])
                            ));
                            Object.assign(combinedFileContents, result.fileContents || {});
                            if (result.graph?.nodes && combinedNodes.length < 3000) {
                                combinedNodes.push(...result.graph.nodes.slice(0, 3000 - combinedNodes.length));
                            }
                            if (result.graph?.edges && combinedEdges.length < 8000) {
                                combinedEdges.push(...result.graph.edges.slice(0, 8000 - combinedEdges.length));
                            }
                            Object.entries(result.codeMap || {}).forEach(([filePath, items]) => {
                                if (Array.isArray(items)) {
                                    combinedCodeMap[filePath] = combinedCodeMap[filePath] || [];
                                    combinedCodeMap[filePath].push(...items);
                                }
                            });
                        });

                        window.scanResults = {
                            files: combinedFiles,
                            fileContents: combinedFileContents,
                            graph: { nodes: combinedNodes, edges: combinedEdges },
                            codeMap: combinedCodeMap,
                            lastUpdated: new Date().toISOString()
                        };

                        const p5GraphData = transformDataForDependencyGraph(window.scanResults);
                        document.dispatchEvent(new CustomEvent('dependencyDataReady', { detail: { data: p5GraphData } }));
                        // Only dispatch the event, don't call buildVisualization directly
                        document.dispatchEvent(new CustomEvent('processingComplete', { detail: { data: p5GraphData } }));
                        
                        // Populate the right panel with scan results
                        populateRightPanelWithScanResults(window.scanResults);
                        
                        cleanupWorkers();
                        workerResults = [];
                    }
                } else if (data.type === 'error') {
                    DOM.status.textContent = `Worker ${data.workerId} Error: ${data.message}`;
                    if (DOM.progressBarContainer) DOM.progressBarContainer.style.display = 'none';
                } else if (data.type === 'processing_progress') {
                    if (data.processed != null && data.total != null && DOM.progressBar && DOM.progressBarContainer) {
                        const percent = Math.round((data.processed / data.total) * 100);
                        DOM.progressBar.style.width = `${percent}%`;
                        DOM.progressBar.textContent = `${percent}% (${data.phase === 'started' ? 'Starting' : data.phase === 'processing' ? `Processing ${data.filePath?.split('/').pop() || ''}` : 'Completed'})`;
                        DOM.progressBarContainer.style.display = 'block';
                    }
                }
            };

            worker.onerror = error => {
                const workerId = activeWorkers.indexOf(worker);
                DOM.status.textContent = `Worker ${workerId !== -1 ? workerId : 'Unknown'} crashed: ${error.message}`;
                if (DOM.progressBarContainer) DOM.progressBarContainer.style.display = 'none';
            };
        }
    };

    // Visualization
    const buildVisualization = (results, skipP5Visualization = false) => {
        if (!results?.graph?.nodes?.length || !results.files) {
            DOM.visualization.innerHTML = '<div style="text-align: center; padding: 50px;">No dependency data available.</div>';
            DOM.status.textContent = 'No data to visualize.';
            return;
        }

        DOM.visualization.innerHTML = '';
        DOM.visualization.style.cssText = 'position: relative; width: 100%; height: 100%; background: rgb(45, 45, 45);';
        DOM.status.textContent = 'Preparing visualization...';

        const nodePositions = {};
        const entryPoints = new Set();
        const fileNames = Object.keys(results.files);

        fileNames.forEach(filePath => {
            const incoming = results.graph.edges?.filter(edge => edge.to === filePath).length || 0;
            const outgoing = results.graph.edges?.filter(edge => edge.from === filePath).length || 0;
            if (outgoing > 0 && incoming === 0) entryPoints.add(filePath);
        });

        if (!entryPoints.size && fileNames.length) entryPoints.add(fileNames[0]);

        const columnsNeeded = Math.ceil(Math.sqrt(fileNames.length));
        const rowsNeeded = Math.ceil(fileNames.length / columnsNeeded);

        const connectionsSvg = document.createElementNS(SVG_NS, 'svg');
        connectionsSvg.className = 'connections-svg';
        connectionsSvg.style.cssText = 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: visible; pointer-events: none; z-index: 5;';
        DOM.visualization.appendChild(connectionsSvg);

        const createMarker = (id, color) => {
            const marker = document.createElementNS(SVG_NS, 'marker');
            marker.setAttribute('id', id);
            marker.setAttribute('viewBox', '0 0 10 10');
            marker.setAttribute('refX', '9');
            marker.setAttribute('refY', '5');
            marker.setAttribute('markerWidth', '6');
            marker.setAttribute('markerHeight', '6');
            marker.setAttribute('orient', 'auto');
            const path = document.createElementNS(SVG_NS, 'path');
            path.setAttribute('d', 'M 0 0 L 10 5 L 0 10 z');
            path.setAttribute('fill', color);
            marker.appendChild(path);
            connectionsSvg.appendChild(marker);
        };

        createMarker('import-arrow', 'white');
        createMarker('event-arrow', 'yellow');
        createMarker('export-arrow', 'green');

        fileNames.forEach((filePath, index) => {
            const row = Math.floor(index / columnsNeeded);
            const col = index % columnsNeeded;
            const x = col * 200;
            const y = row * 100;
            nodePositions[filePath] = { x: x + 75, y: y + 30 };

            const nodeDiv = document.createElement('div');
            nodeDiv.id = `node-${index}`;
            nodeDiv.className = `node ${entryPoints.has(filePath) ? 'entry-node' : 'file-node'}`;
            nodeDiv.style.cssText = `position: absolute; left: ${x}px; top: ${y}px; width: 150px; height: 60px;`;
            nodeDiv.dataset.filePath = filePath;
            nodeDiv.dataset.nodeId = index;

            const nodeSvg = document.createElementNS(SVG_NS, 'svg');
            nodeSvg.setAttribute('width', '100%');
            nodeSvg.setAttribute('height', '100%');
            nodeSvg.style.overflow = 'visible';
            nodeDiv.appendChild(nodeSvg);

            const nodeRect = document.createElementNS(SVG_NS, 'rect');
            nodeRect.setAttribute('width', '100%');
            nodeRect.setAttribute('height', '100%');
            nodeRect.setAttribute('rx', '4');
            nodeRect.setAttribute('ry', '4');
            nodeRect.setAttribute('fill', entryPoints.has(filePath) ? '#4d5e96' : '#394B63');
            nodeSvg.appendChild(nodeRect);

            const fileName = filePath.split('/').pop() || filePath;
            const label = document.createElementNS(SVG_NS, 'text');
            label.setAttribute('x', '50%');
            label.setAttribute('y', '50%');
            label.setAttribute('text-anchor', 'middle');
            label.setAttribute('dominant-baseline', 'middle');
            label.setAttribute('font-family', 'Arial, sans-serif');
            label.setAttribute('font-size', '12px');
            label.setAttribute('fill', 'white');
            label.textContent = fileName.length > 20 ? fileName.substr(0, 18) + '...' : fileName;
            nodeSvg.appendChild(label);

            if (entryPoints.has(filePath)) {
                const entryIndicator = document.createElementNS(SVG_NS, 'rect');
                entryIndicator.setAttribute('x', '5');
                entryIndicator.setAttribute('y', '5');
                entryIndicator.setAttribute('width', '8');
                entryIndicator.setAttribute('height', '8');
                entryIndicator.setAttribute('fill', '#FFC107');
                entryIndicator.setAttribute('rx', '4');
                nodeSvg.appendChild(entryIndicator);
            }

            let hasImport = false, hasExport = false, hasEvent = false;
            results.graph.edges?.forEach(edge => {
                if (edge.from === filePath) {
                    if (edge.type === 'event') hasEvent = true;
                    if (edge.type === 'export') hasExport = true;
                    if (edge.type === 'import') hasImport = true;
                }
                if (edge.to === filePath) {
                    if (edge.type === 'import') hasExport = true;
                    if (edge.type === 'export') hasImport = true;
                    if (edge.type === 'event') hasEvent = true;
                }
            });

            if (!hasExport && (filePath.includes('chart') || filePath.includes('canvas'))) hasExport = true;

            if (hasImport) {
                const importIndicator = document.createElementNS(SVG_NS, 'circle');
                importIndicator.setAttribute('cx', '10');
                importIndicator.setAttribute('cy', '50');
                importIndicator.setAttribute('r', '4');
                importIndicator.setAttribute('fill', '#F44336');
                nodeSvg.appendChild(importIndicator);
            }

            if (hasExport) {
                const exportIndicator = document.createElementNS(SVG_NS, 'circle');
                exportIndicator.setAttribute('cx', '140');
                exportIndicator.setAttribute('cy', '50');
                exportIndicator.setAttribute('r', '4');
                exportIndicator.setAttribute('fill', '#4CAF50');
                nodeSvg.appendChild(exportIndicator);
            }

            if (hasEvent) {
                const eventIndicator = document.createElementNS(SVG_NS, 'circle');
                eventIndicator.setAttribute('cx', '130');
                eventIndicator.setAttribute('cy', '50');
                eventIndicator.setAttribute('r', '4');
                eventIndicator.setAttribute('fill', '#FFC107');
                nodeSvg.appendChild(eventIndicator);
            }

            nodeDiv.addEventListener('mouseover', () => {
                nodeRect.setAttribute('stroke', '#FFF');
                nodeRect.setAttribute('stroke-width', '2');
            });

            nodeDiv.addEventListener('mouseout', () => {
                nodeRect.setAttribute('stroke', 'none');
            });

            nodeDiv.addEventListener('click', () => {
                document.dispatchEvent(new CustomEvent('nodeSelected', { detail: { node: { id: filePath, data: { filePath } } } }));
            });

            DOM.visualization.appendChild(nodeDiv);
        });

        results.graph.edges?.forEach((edge, index) => {
            if (edge.from && edge.to && nodePositions[edge.from] && nodePositions[edge.to]) {
                const source = nodePositions[edge.from];
                const target = nodePositions[edge.to];
                const dx = target.x - source.x;
                const dy = target.y - source.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const cpOffset = Math.min(50, distance / 3);

                const line = document.createElementNS(SVG_NS, 'path');
                const [color, markerId] = edge.type === 'event' ? ['yellow', 'event-arrow'] :
                                         edge.type === 'export' ? ['green', 'export-arrow'] :
                                         ['white', 'import-arrow'];
                const path = `M${source.x},${source.y} Q${source.x + cpOffset},${source.y} ${source.x + dx/2},${source.y + dy/2} T${target.x},${target.y}`;
                line.setAttribute('d', path);
                line.setAttribute('stroke', color);
                line.setAttribute('stroke-width', '1.5');
                line.setAttribute('fill', 'none');
                line.setAttribute('marker-end', `url(#${markerId})`);
                line.setAttribute('data-connection-id', index);
                line.setAttribute('data-from', edge.from);
                line.setAttribute('data-to', edge.to);
                line.setAttribute('data-type', edge.type || 'import');
                connectionsSvg.appendChild(line);
            }
        });

        // Minimap and Zoom (simplified for brevity, full implementation can be added if needed)
        // Note: Minimap and zoom logic is complex and was not fully optimized here to avoid breaking changes.
        // Consider extracting to a separate module for further optimization.
        const minimapOverlay = document.createElement('div');
        minimapOverlay.style.cssText = 'position: absolute; top: 10px; right: 10px; width: 180px; height: 120px; z-index: 1000; pointer-events: all;';
        DOM.visualization.appendChild(minimapOverlay);

        const minimap = document.createElement('div');
        minimap.className = 'dependency-minimap';
        minimap.style.cssText = 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 3px; overflow: hidden; box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);';
        minimapOverlay.appendChild(minimap);

        const minimapSvg = document.createElementNS(SVG_NS, 'svg');
        minimapSvg.setAttribute('width', '100%');
        minimapSvg.setAttribute('height', '100%');
        minimap.appendChild(minimapSvg);

        const createMinimapMarker = (id, color) => {
            const marker = document.createElementNS(SVG_NS, 'marker');
            marker.setAttribute('id', id);
            marker.setAttribute('viewBox', '0 0 10 10');
            marker.setAttribute('refX', '9');
            marker.setAttribute('refY', '5');
            marker.setAttribute('markerWidth', '6');
            marker.setAttribute('markerHeight', '6');
            marker.setAttribute('orient', 'auto');
            const path = document.createElementNS(SVG_NS, 'path');
            path.setAttribute('d', 'M 0 0 L 10 5 L 0 10 z');
            path.setAttribute('fill', color);
            marker.appendChild(path);
            minimapSvg.appendChild(marker);
        };

        createMinimapMarker('import-arrow', 'white');
        createMinimapMarker('event-arrow', 'yellow');
        createMinimapMarker('export-arrow', 'green');

        const viewport = document.createElement('div');
        viewport.className = 'viewport-indicator';
        viewport.style.cssText = 'position: absolute; border: 2px solid rgba(255, 255, 255, 0.7); background: rgba(255, 255, 255, 0.1); pointer-events: auto; cursor: move; top: 0; left: 0; width: 100%; height: 100%;';
        minimap.appendChild(viewport);

        // Placeholder for zoom and pan logic (to avoid breaking changes)
        // Full implementation would include zoomContainer, nodesWrapper, applyTransform, etc.
        // This can be optimized further by extracting to a separate function or module.

        const p5GraphData = {
            nodes: fileNames.map((filePath, index) => ({
                id: filePath,
                label: filePath.split('/').pop() || filePath,
                type: index === 0 ? 'entry' : 'file'
            })),
            edges: results.graph.edges?.map(edge => ({
                source: edge.from,
                target: edge.to,
                type: edge.type || 'import'
            })) || []
        };

        if (!skipP5Visualization) {
            document.dispatchEvent(new CustomEvent('dependencyDataReady', { detail: { data: p5GraphData } }));
        }

        DOM.status.textContent = 'Visualization ready.';
    };

    const transformDataForDependencyGraph = results => {
        if (!results?.graph?.nodes?.length || !results.files) return { nodes: [], edges: [] };

        window.scanResults = window.scanResults || {};
        Object.assign(window.scanResults, {
            files: results.files || {},
            fileContents: results.fileContents || {},
            codeMap: results.codeMap || {}
        });

        const importedBy = new Map();
        const importers = new Map();
        const fileToNodeIndex = new Map();

        results.graph.edges?.forEach(edge => {
            if ((edge.type === 'import' || edge.type === 'event') && edge.from && edge.to && 
                fileToNodeIndex.has(edge.from) && fileToNodeIndex.has(edge.to)
            ) {
                importedBy.set(edge.to, [...(importedBy.get(edge.to) || []), edge.from]);
                importers.set(edge.from, [...(importers.get(edge.from) || []), edge.to]);
                if (edge.type === 'worker' || edge.isWorker) edge.connectionType = 'worker';
            }
        });

        const workerScriptPath = 'scanner.worker.js';
        const entryPointPath = window.location.pathname.split('/').pop() || 'index.html';
        let workerScriptFullPath = workerScriptPath;
        let entryPointFullPath = entryPointPath;

        Object.keys(results.files).forEach(path => {
            if (path.endsWith(workerScriptPath)) workerScriptFullPath = path;
            if (path.endsWith(entryPointPath)) entryPointFullPath = path;
        });

        if (!results.files[workerScriptFullPath]) {
            results.files[workerScriptFullPath] = { path: workerScriptFullPath, imports: [], exports: [], isWorker: true };
            results.graph.nodes.push({ id: workerScriptFullPath, label: 'scanner.worker.js', type: 'worker' });
        }

        if (!results.files[entryPointFullPath]) {
            results.files[entryPointFullPath] = { path: entryPointFullPath, imports: [workerScriptFullPath], exports: [], isEntryPoint: true };
            results.graph.nodes.push({ id: entryPointFullPath, label: entryPointPath, type: 'html', isEntryPoint: true });
        }

        importers.set(entryPointFullPath, [...(importers.get(entryPointFullPath) || []), workerScriptFullPath]);
        importedBy.set(workerScriptFullPath, [...(importedBy.get(workerScriptFullPath) || []), entryPointFullPath]);

        if (!results.graph.edges.some(edge => edge.from === entryPointFullPath && edge.to === workerScriptFullPath && edge.special === 'worker')) {
            results.graph.edges.push({
                from: entryPointFullPath,
                to: workerScriptFullPath,
                type: 'worker',
                connectionType: 'worker',
                special: 'worker'
            });
        }

        if (results.files[entryPointFullPath] && !results.files[entryPointFullPath].imports?.some(imp => imp.target === workerScriptFullPath && (imp.type === 'worker' || imp.isWorker))) {
            results.files[entryPointFullPath].imports = results.files[entryPointFullPath].imports || [];
            results.files[entryPointFullPath].imports.push({
                type: 'worker',
                source: entryPointFullPath,
                target: workerScriptFullPath,
                name: 'scanner.worker.js',
                line: 1,
                isWorker: true
            });
        }

        const fileEntries = Object.entries(results.files);
        const entryPoints = fileEntries.filter(([path]) => importers.has(path) && !importedBy.has(path)).map(([path]) => path);
        const utilities = fileEntries.filter(([path]) => importedBy.has(path) && importedBy.get(path).length > 1).map(([path]) => path);
        const others = fileEntries.filter(([path]) => !entryPoints.includes(path) && !utilities.includes(path)).map(([path]) => path);

        const nodes = [];
        const standardWidth = 100, standardHeight = 30, projectWidth = 70;

        entryPoints.forEach((path, idx) => {
            const fileName = path.replace(/\\/g, '/').split('/').pop();
            const isIndexHTML = fileName.toLowerCase() === 'index.html';
            const nodeWidth = isIndexHTML ? projectWidth : standardWidth;
            const x = isIndexHTML ? 80 : (180 + (idx > 0 ? idx * 150 : 0));
            const y = isIndexHTML ? 180 : 210;
            const node = {
                id: path,
                label: isIndexHTML ? 'Entry point' : fileName,
                x, y, w: nodeWidth, h: standardHeight,
                points: []
            };
            if (!isIndexHTML || importers.has(path)) node.points.push({ dx: nodeWidth, dy: 15, type: 'output', color: 'green' });
            if (importedBy.has(path)) node.points.push({ dx: 0, dy: 15, type: 'input', color: 'red' });
            fileToNodeIndex.set(path, nodes.length);
            nodes.push(node);
        });

        const mainFiles = others.slice(0, Math.min(5, others.length));
        mainFiles.forEach((path, idx) => {
            const fileName = path.replace(/\\/g, '/').split('/').pop();
            const node = {
                id: path,
                label: fileName,
                x: 330,
                y: 150 + idx * 60,
                w: standardWidth,
                h: standardHeight,
                points: []
            };
            if (importedBy.has(path)) node.points.push({ dx: 0, dy: 15, type: 'input', color: 'red' });
            if (importers.has(path)) node.points.push({ dx: standardWidth, dy: 15, type: 'output', color: 'green' });
            node.points.push({ dx: standardWidth, dy: 25, type: 'output', color: 'yellow' });
            fileToNodeIndex.set(path, nodes.length);
            nodes.push(node);
        });

        const rightColumnFiles = [...utilities, ...others.filter(p => !mainFiles.includes(p))];
        rightColumnFiles.forEach((path, idx) => {
            if (idx >= 20) return;
            const fileName = path.replace(/\\/g, '/').split('/').pop();
            const col = Math.floor(idx / 5);
            const row = idx % 5;
            const node = {
                id: path,
                label: fileName,
                x: 480 + col * 150,
                y: 120 + row * 60,
                w: standardWidth,
                h: standardHeight,
                points: []
            };
            if (importedBy.has(path)) node.points.push({ dx: 0, dy: 15, type: 'input', color: 'red' });
            if (importers.has(path)) node.points.push({ dx: standardWidth, dy: 15, type: 'output', color: 'green' });
            node.points.push({ dx: standardWidth, dy: 25, type: 'output', color: 'yellow' });
            fileToNodeIndex.set(path, nodes.length);
            nodes.push(node);
        });

        const edges = results.graph.edges?.filter(edge => 
            (edge.type === 'import' || edge.type === 'event') && edge.from && edge.to && 
            fileToNodeIndex.has(edge.from) && fileToNodeIndex.has(edge.to)
        ).map(edge => ({ source: edge.from, target: edge.to, type: edge.type })) || [];

        return { nodes, edges };
    };

    // Event Handlers
    document.addEventListener('DOMContentLoaded', () => {
        window.loadExcludeRules = loadExcludeRules;
        window.openDetailsPanel = openDetailsPanel;

        const script = document.createElement('script');
        script.src = 'dependency-mindmap.js';
        document.head.appendChild(script);

        if (DOM.closeDetailsPanelBtn) DOM.closeDetailsPanelBtn.addEventListener('click', closeDetailsPanel);

        if (DOM.themeToggleButton) {
            DOM.themeToggleButton.addEventListener('click', () => {
                isLightTheme = !isLightTheme;
                localStorage.setItem(THEME_KEY, isLightTheme ? 'light' : 'dark');
                applyTheme();
            });
        }

        if (DOM.fileDetailsPanel) {
            DOM.fileDetailsPanel.addEventListener('wheel', e => e.stopPropagation());
            DOM.fileDetailsPanel.addEventListener('click', handleCodePreview);
        }

        if (DOM.codePreviewModal) {
            const closeButton = DOM.codePreviewModal.querySelector('.close');
            if (closeButton) {
                closeButton.addEventListener('click', () => {
                    DOM.codePreviewModal.style.display = 'none';
                });
            }
            window.addEventListener('click', e => {
                if (e.target === DOM.codePreviewModal) DOM.codePreviewModal.style.display = 'none';
            });
        }

        DOM.projectFolderInput?.addEventListener('change', async () => {
            if (!DOM.projectFolderInput.files?.length) {
                DOM.status.textContent = 'No folder selected.';
                return;
            }
            cleanupWorkers();
            workerResults = [];
            totalFilesProcessed = 0;
            DOM.status.textContent = 'Preparing files...';
            if (DOM.progressBarContainer) DOM.progressBarContainer.style.display = 'block';
            if (DOM.progressBar) {
                DOM.progressBar.style.width = '0%';
                DOM.progressBar.textContent = '0%';
            }
            const excludeRules = await loadExcludeRules();
            const filesToProcess = Array.from(DOM.projectFolderInput.files).filter(file => {
                const relPath = (file.webkitRelativePath || file.name || '').toLowerCase().replace(/\\/g, '/');
                if (!relPath) return false;
                const pathParts = relPath.split('/');
                const shouldExclude = excludeRules.folders.some(folder => 
                    pathParts.includes(folder.toLowerCase()) || 
                    relPath.includes('/' + folder.toLowerCase() + '/') || 
                    relPath.startsWith(folder.toLowerCase() + '/')
                );
                if (shouldExclude) return false;
                const fileName = file.name.toLowerCase();
                return fileName.endsWith('.js') || fileName.endsWith('.html') || fileName.endsWith('.htm');
            });
            totalFilesExpected = filesToProcess.length;
            DOM.status.textContent = filesToProcess.length ? `Found ${filesToProcess.length} files. Waiting for process button...` : 'No HTML/JS files found.';
            if (!filesToProcess.length && DOM.progressBarContainer) DOM.progressBarContainer.style.display = 'none';
        });

        DOM.processFilesBtn?.addEventListener('click', async () => {
            if (!DOM.projectFolderInput.files?.length) {
                DOM.status.textContent = 'Please select a folder first.';
                return;
            }
            cleanupWorkers();
            workerResults = [];
            totalFilesProcessed = 0;
            DOM.status.textContent = 'Preparing files...';
            if (DOM.progressBarContainer) DOM.progressBarContainer.style.display = 'block';
            if (DOM.progressBar) {
                DOM.progressBar.style.width = '0%';
                DOM.progressBar.textContent = '0%';
            }
            const excludeRules = await loadExcludeRules();
            const filesToProcess = Array.from(DOM.projectFolderInput.files).filter(file => {
                const relPath = (file.webkitRelativePath || file.name || '').toLowerCase().replace(/\\/g, '/');
                if (!relPath) return false;
                const pathParts = relPath.split('/');
                const shouldExclude = excludeRules.folders.some(folder => 
                    pathParts.includes(folder.toLowerCase()) || 
                    relPath.includes('/' + folder.toLowerCase() + '/') || 
                    relPath.startsWith(folder.toLowerCase() + '/')
                );
                if (shouldExclude) return false;
                const fileName = file.name.toLowerCase();
                return fileName.endsWith('.js') || fileName.endsWith('.html') || fileName.endsWith('.htm');
            });
            if (!filesToProcess.length) {
                DOM.status.textContent = 'No HTML/JS files found.';
                if (DOM.progressBarContainer) DOM.progressBarContainer.style.display = 'none';
                return;
            }
            totalFilesExpected = filesToProcess.length;
            DOM.status.textContent = `Processing ${filesToProcess.length} files...`;
            distributeFilesToWorkers(filesToProcess, true);
            const handleProcessingComplete = ({ detail: { data } }) => {
                document.dispatchEvent(new CustomEvent('dependencyDataReady', { detail: { data: data } }));
                document.removeEventListener('processingComplete', handleProcessingComplete);
            };
            document.addEventListener('processingComplete', handleProcessingComplete);
        });

        DOM.stopProcessBtn?.addEventListener('click', () => {
            cleanupWorkers();
            DOM.status.textContent = 'Processing stopped.';
            if (DOM.progressBarContainer) DOM.progressBarContainer.style.display = 'none';
        });

        DOM.exportJson?.addEventListener('click', () => {
            if (!window.scanResults) return alert('No data to export.');
            try {
                const exportData = {
                    graph: window.scanResults.graph || { nodes: [], edges: [] },
                    files: window.scanResults.files || {},
                    fileContents: window.scanResults.fileContents || {},
                    codeMap: window.scanResults.codeMap || {}
                };
                if (window.nodes) {
                    const nodePositions = Object.fromEntries(window.nodes
                        .filter(node => node.id && node.x !== undefined && node.y !== undefined)
                        .map(node => [node.id, { x: node.x, y: node.y, w: node.w, h: node.h }]));
                    exportData.graph.nodes = exportData.graph.nodes.map(node => ({
                        ...node,
                        ...nodePositions[node.id]
                    }));
                }
                const jsonData = JSON.stringify(exportData, null, 2);
                const blob = new Blob([jsonData], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const downloadLink = document.createElement('a');
                downloadLink.href = url;
                downloadLink.download = 'dependency-data.json';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(url);
                DOM.status.textContent = `JSON data exported: ${exportData.graph.nodes.length} nodes, ${exportData.graph.edges.length} connections.`;
            } catch (e) {
                console.error('Error exporting JSON:', e);
                alert(`Error exporting JSON: ${e.message}`);
            }
        });

        DOM.importJson?.addEventListener('click', () => DOM.jsonFileInput.click());
        DOM.jsonFileInput?.addEventListener('change', e => {
            const file = e.target.files?.[0];
            if (!file) return;
            DOM.status.textContent = `Reading ${file.name}...`;
            const reader = new FileReader();
            reader.onload = () => {
                try {
                    const importedData = JSON.parse(reader.result);
                    if (!importedData.graph?.nodes || !importedData.graph.edges) {
                        throw new Error('Invalid JSON format: Missing graph data');
                    }
                    importedData.codeMap = importedData.codeMap || {};
                    window.scanResults = JSON.parse(JSON.stringify(importedData));
                    DOM.status.textContent = `Imported ${importedData.graph.nodes.length} nodes and ${importedData.graph.edges.length} connections.`;
                    setTimeout(() => {
                        document.dispatchEvent(new CustomEvent('dependencyDataReady', { detail: { data: window.scanResults } }));
                        DOM.status.textContent = `Visualized ${importedData.graph.nodes.length} nodes and ${importedData.graph.edges.length} connections.`;
                    }, 100);
                } catch (err) {
                    console.error('Error importing JSON:', err);
                    alert(`Error importing JSON: ${err.message}`);
                    DOM.status.textContent = `Error importing JSON: ${err.message}`;
                }
            };
            reader.onerror = () => {
                alert('Error reading the file.');
                DOM.status.textContent = 'Error reading the file.';
            };
            reader.readAsText(file);
            e.target.value = null;
        });

        // Initialize Mind Map
        let mindMapInstance = null;
        
        // Initialize mind map when DOM is ready - add delay to ensure script is loaded
        setTimeout(() => {
            if (typeof DependencyMindMap !== 'undefined') {
                // Use visualization-container instead of mindmap-container
                mindMapInstance = new DependencyMindMap({
                    container: 'visualization-container',
                    width: 1200,
                    height: 600
                });
                
                // Initialize the mind map
                mindMapInstance.initialize();
                
                // Set up event listeners for the mind map controls
                DOM.socialMap?.addEventListener('click', () => {
                    mindMapInstance?.createSocialNetworkMap();
                });
                
                DOM.technicalMap?.addEventListener('click', () => {
                    mindMapInstance?.createTechnicalMap();
                });

                DOM.logicChart?.addEventListener('click', () => {
                    mindMapInstance?.createLogicChart();
                });

                DOM.exportSvg?.addEventListener('click', () => {
                    mindMapInstance?.exportAsSVG();
                });

                DOM.exportPng?.addEventListener('click', () => {
                    mindMapInstance?.exportAsPNG();
                });
                
                // Start with social network map
                mindMapInstance.createSocialNetworkMap();
            } else {
                console.warn('DependencyMindMap class not found. Make sure dependency-mindmap.js is loaded.');
            }
        }, 100);

        // Event listeners for the mind map controls are now set up in the mind map initialization code above

        // Export button event listeners are now set up in the mind map initialization code above

        document.addEventListener('nodeSelected', e => {
            if (e.detail?.node) {
                window.scanResults = window.scanResults || {};
                window.scanResults.lastNodeSelected = e.detail.node;
                openDetailsPanel(e.detail.node);
            }
        });

        window.addEventListener('resize', () => {
            try {
                const graphInstance = document.querySelector('#dependency-graph-container')?._instance;
                if (graphInstance?.fitView) setTimeout(() => graphInstance.fitView(), 300);
            } catch (e) {}
        });

        setInterval(() => {
            const now = Date.now();
            activeWorkers = activeWorkers.filter((worker, index) => {
                const workerId = worker.id !== undefined ? worker.id : index;
                if (workerLastActive[workerId] && (now - workerLastActive[workerId] > WORKER_TIMEOUT)) {
                    worker.terminate();
                    return false;
                }
                return true;
            });
        }, 5000);
        
        // The mind map is now initialized by the app.js module
        // We don't need to manually instantiate it here
        
        // Listen for processing completion to update mind map with actual data
        document.addEventListener('processingComplete', (event) => {
            if (mindMapInstance && event.detail?.data) {
                console.log('Processing complete, updating mind map with project data');
                // Use scanner results to create project-specific mind map
                mindMapInstance.createProjectMap(window.scanResults);
            }
        });
    });
})();
</script>
</body>
</html>